import numpy as np
import matplotlib.pyplot as plt
from hw3_utils import *

# Set matplotlib parameters for better plots
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# File paths
ecg_file = '../data/lab3/DV1_ecg_p.txt'
fsr_file = '../data/lab3/DV1_fsr_p.txt'

# Load data
ecg_signal, ecg_time, fsr_signal, fsr_time = load_synchronized_data(ecg_file, fsr_file)

print(f"ECG signal length: {len(ecg_signal)} samples")
print(f"FSR signal length: {len(fsr_signal)} samples")
print(f"ECG time range: {ecg_time[0]:.2f} - {ecg_time[-1]:.2f} seconds")
print(f"FSR time range: {fsr_time[0]:.2f} - {fsr_time[-1]:.2f} seconds")

# Plot raw signals
fig = plot_signals(
    ecg_time, ecg_signal, 
    fsr_time, fsr_signal,
    'Raw ECG Signal', 'Raw FSR Signal (Channel 2)',
    'Amplitude (mV)', 'Amplitude (ADC units)'
)
plt.show()

# Estimate sampling frequency from time data
fs_ecg = 1 / np.mean(np.diff(ecg_time))
fs_fsr = 1 / np.mean(np.diff(fsr_time))

print(f"ECG sampling frequency: {fs_ecg:.1f} Hz")
print(f"FSR sampling frequency: {fs_fsr:.1f} Hz")

# Apply bandpass filtering
ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
fsr_filtered = preprocess_signal(fsr_signal, lowcut=0.5, highcut=10, fs=fs_fsr)

print("Bandpass filtering completed.")

# Plot filtered signals
fig = plot_signals(
    ecg_time, ecg_filtered, 
    fsr_time, fsr_filtered,
    'Filtered ECG Signal (0.5-40 Hz)', 'Filtered FSR Signal (0.5-10 Hz)',
    'Amplitude (mV)', 'Amplitude (ADC units)'
)
plt.show()

# Apply DWT reconstruction
ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='bior3.9', level=4)
fsr_dwt = apply_dwt_reconstruction(fsr_filtered, wavelet='bior3.9', level=4)

# Ensure same length (DWT might change signal length slightly)
min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
min_len_fsr = min(len(fsr_filtered), len(fsr_dwt))

ecg_dwt = ecg_dwt[:min_len_ecg]
fsr_dwt = fsr_dwt[:min_len_fsr]
ecg_time_dwt = ecg_time[:min_len_ecg]
fsr_time_dwt = fsr_time[:min_len_fsr]

print("DWT reconstruction completed.")

# Plot DWT reconstructed signals
fig = plot_signals(
    ecg_time_dwt, ecg_dwt, 
    fsr_time_dwt, fsr_dwt,
    'DWT Reconstructed ECG Signal', 'DWT Reconstructed FSR Signal',
    'Amplitude (mV)', 'Amplitude (ADC units)'
)
plt.show()

# Find R peaks in ECG
r_peaks = find_r_peaks(ecg_dwt, fs_ecg)
print(f"Found {len(r_peaks)} R peaks in ECG")

# Find turning points in FSR
fsr_peaks = find_fsr_turning_points(fsr_dwt, fs_fsr)
print(f"Found {len(fsr_peaks)} peaks in FSR")

# Plot ECG with R peaks
fig1 = plot_peaks_detection(
    ecg_time_dwt, ecg_dwt, r_peaks,
    'ECG Signal with Detected R Peaks', 'Amplitude (mV)'
)
plt.show()

# Plot FSR with turning points
fig2 = plot_peaks_detection(
    fsr_time_dwt, fsr_dwt, fsr_peaks,
    'FSR Signal with Detected Turning Points', 'Amplitude (ADC units)'
)
plt.show()

# Calculate PTT
ptt_ms, matched_ecg_peaks, matched_fsr_peaks = calculate_ptt(r_peaks, fsr_peaks, fs_ecg)

print(f"Successfully matched {len(ptt_ms)} peak pairs")
print(f"PTT statistics:")
print(f"  Mean: {np.mean(ptt_ms):.1f} ms")
print(f"  Std:  {np.std(ptt_ms):.1f} ms")
print(f"  Min:  {np.min(ptt_ms):.1f} ms")
print(f"  Max:  {np.max(ptt_ms):.1f} ms")

# Plot a zoomed section showing PTT calculation
if len(matched_ecg_peaks) > 0:
    # Select a time window around the first few matched peaks
    start_idx = max(0, matched_ecg_peaks[0] - int(fs_ecg * 2))  # 2 seconds before
    end_idx = min(len(ecg_dwt), matched_ecg_peaks[min(4, len(matched_ecg_peaks)-1)] + int(fs_ecg * 2))  # 2 seconds after
    
    # Create time vectors for the zoomed section
    ecg_zoom_time = ecg_time_dwt[start_idx:end_idx]
    ecg_zoom_signal = ecg_dwt[start_idx:end_idx]
    
    # Find corresponding FSR indices
    fsr_start_time = ecg_zoom_time[0]
    fsr_end_time = ecg_zoom_time[-1]
    fsr_start_idx = np.argmin(np.abs(fsr_time_dwt - fsr_start_time))
    fsr_end_idx = np.argmin(np.abs(fsr_time_dwt - fsr_end_time))
    
    fsr_zoom_time = fsr_time_dwt[fsr_start_idx:fsr_end_idx]
    fsr_zoom_signal = fsr_dwt[fsr_start_idx:fsr_end_idx]
    
    # Plot zoomed signals with matched peaks
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # ECG plot
    ax1.plot(ecg_zoom_time, ecg_zoom_signal, 'b-', label='ECG')
    
    # Mark matched ECG peaks in the zoom window
    for peak_idx in matched_ecg_peaks[:5]:  # Show first 5 matched peaks
        if start_idx <= peak_idx < end_idx:
            ax1.plot(ecg_time_dwt[peak_idx], ecg_dwt[peak_idx], 'ro', markersize=10, label='R Peak' if peak_idx == matched_ecg_peaks[0] else "")
    
    ax1.set_title('ECG Signal with R Peaks (Zoomed View)')
    ax1.set_ylabel('Amplitude (mV)')
    ax1.grid(True)
    ax1.legend()
    
    # FSR plot
    ax2.plot(fsr_zoom_time, fsr_zoom_signal, 'g-', label='FSR')
    
    # Mark matched FSR peaks
    for i, fsr_peak_idx in enumerate(matched_fsr_peaks[:5]):
        if fsr_start_idx <= fsr_peak_idx < fsr_end_idx:
            ax2.plot(fsr_time_dwt[fsr_peak_idx], fsr_dwt[fsr_peak_idx], 'mo', markersize=10, label='FSR Peak' if i == 0 else "")
    
    ax2.set_title('FSR Signal with Turning Points (Zoomed View)')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Amplitude (ADC units)')
    ax2.grid(True)
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

# Plot PTT time series
if len(ptt_ms) > 0:
    beat_times = ecg_time_dwt[matched_ecg_peaks]
    
    plt.figure(figsize=(12, 6))
    plt.plot(beat_times, ptt_ms, 'bo-', markersize=6, linewidth=2)
    plt.title('Pulse Transit Time (PTT) Over Time')
    plt.xlabel('Time (s)')
    plt.ylabel('PTT (ms)')
    plt.grid(True)
    
    # Add statistics as text
    plt.text(0.02, 0.98, f'Mean PTT: {np.mean(ptt_ms):.1f} ms\nStd PTT: {np.std(ptt_ms):.1f} ms', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

# Estimate blood pressure
if len(ptt_ms) > 0:
    systolic_bp, diastolic_bp = estimate_bp_linear(ptt_ms)
    
    print("Blood Pressure Estimation Results:")
    print(f"Systolic BP - Mean: {np.mean(systolic_bp):.1f} mmHg, Std: {np.std(systolic_bp):.1f} mmHg")
    print(f"Diastolic BP - Mean: {np.mean(diastolic_bp):.1f} mmHg, Std: {np.std(diastolic_bp):.1f} mmHg")
    print(f"Pulse Pressure - Mean: {np.mean(systolic_bp - diastolic_bp):.1f} mmHg")

# Plot blood pressure estimates
if len(ptt_ms) > 0:
    beat_times = ecg_time_dwt[matched_ecg_peaks]
    
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(beat_times, systolic_bp, 'ro-', label='Systolic BP', markersize=6)
    plt.plot(beat_times, diastolic_bp, 'bo-', label='Diastolic BP', markersize=6)
    plt.title('Estimated Blood Pressure Over Time')
    plt.ylabel('Blood Pressure (mmHg)')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.plot(beat_times, ptt_ms, 'go-', markersize=6)
    plt.title('Pulse Transit Time vs Blood Pressure')
    plt.xlabel('Time (s)')
    plt.ylabel('PTT (ms)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()