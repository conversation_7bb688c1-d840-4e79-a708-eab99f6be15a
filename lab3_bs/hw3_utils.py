import numpy as np
import pandas as pd
import pywt
import scipy.signal as signal
import matplotlib.pyplot as plt


def load_synchronized_data(ecg_file, fsr_file):
    """Load and synchronize ECG and FSR data from text files."""
    # Load ECG data
    ecg_data = pd.read_csv(ecg_file, sep=";")
    ecg_signal = ecg_data["ECG II"].values
    ecg_time = ecg_data["ctime"].values

    # Load FSR data (using channel 2 as specified)
    fsr_data = pd.read_csv(fsr_file, sep=";")
    fsr_signal = fsr_data["v2"].values
    fsr_time = fsr_data["ctime"].values

    return ecg_signal, ecg_time, fsr_signal, fsr_time


def preprocess_signal(signal_data, lowcut, highcut, fs, order=4):
    """Apply bandpass filter to signal."""
    nyquist = 0.5 * fs
    low = lowcut / nyquist
    high = highcut / nyquist
    b, a = signal.butter(order, [low, high], btype="band")
    return signal.filtfilt(b, a, signal_data)


def apply_dwt_reconstruction(signal_data, wavelet="bior3.9", level=4):
    """Apply DWT decomposition and reconstruction."""
    coeffs = pywt.wavedec(signal_data, wavelet, level=level)
    reconstructed = pywt.waverec(coeffs, wavelet)
    return reconstructed


def find_r_peaks(ecg_signal, fs, height_threshold=None):
    """Find R peaks in ECG signal."""
    if height_threshold is None:
        height_threshold = np.std(ecg_signal) * 2

    peaks, _ = signal.find_peaks(
        ecg_signal,
        distance=int(fs * 0.4),  # Minimum 400ms between peaks
        height=height_threshold,
        prominence=height_threshold * 0.5,
    )
    return peaks


def find_fsr_turning_points(fsr_signal, fs):
    """Find turning points (peaks) in FSR signal."""
    # Smooth the signal first
    wl = int(fs * 0.1)
    if wl % 2 == 0:
        wl += 1
    # window_length=wl, polyorder=3, deriv=0→Signal, deriv=1→ erste Ableitung
    smoothed = signal.savgol_filter(fsr_signal, window_length=wl, polyorder=3, deriv=0)
    deriv1 = signal.savgol_filter(
        fsr_signal, window_length=wl, polyorder=3, deriv=1, delta=1 / fs
    )

    # 2) Suche nach Stellen, an denen deriv1 von >0 zu <0 wechselt → lokale Maxima
    signs = np.sign(deriv1)
    zero_crossings = np.where((signs[:-1] > 0) & (signs[1:] < 0))[0]

    # 3) Optional: mit Mindestabstand und Prominenz filtern
    peaks, props = signal.find_peaks(
        smoothed, distance=int(fs * 0.4), prominence=np.std(smoothed) * 0.5
    )
    # nur die zero_crossings behalten, die nahe an einem gefilterten Peak liegen
    final_peaks = [
        zc for zc in zero_crossings if np.any(np.abs(peaks - zc) <= int(fs * 0.1))
    ]

    return np.array(final_peaks)


def calculate_ptt(ecg_peaks, fsr_turning_points, fs):
    """Calculate pulse transit time between ECG R peaks and FSR turning points."""
    ptt_samples = []
    matched_ecg_peaks = []
    matched_fsr_peaks = []

    for r_peak in ecg_peaks:
        # Find the next FSR peak after the R peak (FSR lags behind ECG)
        fsr_candidates = fsr_turning_points[fsr_turning_points > r_peak]
        if len(fsr_candidates) > 0:
            fsr_peak = fsr_candidates[0]
            ptt_samples.append(fsr_peak - r_peak)
            matched_ecg_peaks.append(r_peak)
            matched_fsr_peaks.append(fsr_peak)

    # Convert samples to time (ms)
    ptt_ms = np.array(ptt_samples) * (1000 / fs)

    return ptt_ms, np.array(matched_ecg_peaks), np.array(matched_fsr_peaks)


def estimate_bp_linear(ptt_ms, systolic_ref=120, diastolic_ref=80, ptt_ref=250):
    """Estimate blood pressure using simple linear model."""
    # Simple linear relationship: BP decreases as PTT increases
    # This is a simplified model - in practice, calibration would be needed

    # Calculate slope based on typical PTT-BP relationship
    ptt_sensitivity = -0.5  # mmHg/ms (typical value from literature)

    systolic_bp = systolic_ref + ptt_sensitivity * (ptt_ms - ptt_ref)
    diastolic_bp = diastolic_ref + ptt_sensitivity * (ptt_ms - ptt_ref)

    return systolic_bp, diastolic_bp


def plot_signals(time1, signal1, time2, signal2, title1, title2, ylabel1, ylabel2):
    """Plot two signals in subplots."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    ax1.plot(time1, signal1)
    ax1.set_title(title1)
    ax1.set_ylabel(ylabel1)
    ax1.grid(True)

    ax2.plot(time2, signal2)
    ax2.set_title(title2)
    ax2.set_xlabel("Time (s)")
    ax2.set_ylabel(ylabel2)
    ax2.grid(True)

    plt.tight_layout()
    return fig


def plot_peaks_detection(time, signal, peaks, title, ylabel):
    """Plot signal with detected peaks."""
    fig, ax = plt.subplots(figsize=(12, 6))
    ax.plot(time, signal, label="Signal")
    ax.plot(time[peaks], signal[peaks], "ro", markersize=8, label="Detected Peaks")
    ax.set_title(title)
    ax.set_xlabel("Time (s)")
    ax.set_ylabel(ylabel)
    ax.legend()
    ax.grid(True)
    return fig
