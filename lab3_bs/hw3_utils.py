import wfdb
import numpy as np
import pandas as pd
import pywt
import scipy.signal as signal
import matplotlib.pyplot as plt


def download_sample_record(record_name, record_path="bidmc"):
    wfdb.dl_database(record_path, dl_dir="./data", records=[record_name])


def load_signals(record_name):
    record = wfdb.rdrecord(f"./data/{record_name}")
    ecg_signal = record.p_signal[:, 1]  # Assuming ECG is the second signal
    ppg_signal = record.p_signal[:, 2]  # Assuming PPG is the third signal
    fs = record.fs  # Sampling frequency
    return ecg_signal, ppg_signal, fs


def preprocess_signal(signal_data, lowcut, highcut, fs, order=1):
    nyquist = 0.5 * fs
    low = lowcut / nyquist
    high = highcut / nyquist
    b, a = signal.butter(order, [low, high], btype="band")
    return signal.filtfilt(b, a, signal_data)


def apply_dwt(signal_data, wavelet, level):
    coeffs = pywt.wavedec(signal_data, wavelet, level=level)
    return coeffs


def find_j_peak(ecg_signal, fs):
    peaks, _ = signal.find_peaks(ecg_signal, distance=fs / 2)
    return peaks


def find_ppg_turning_point(ppg_signal, fs):
    derivative = np.diff(ppg_signal)
    turning_points, _ = signal.find_peaks(-derivative, distance=fs/2)
    return turning_points


def calculate_ptt(ecg_peaks, ppg_turning_points):
    ptt = []
    for j_peak in ecg_peaks:
        ppg_point = ppg_turning_points[ppg_turning_points > j_peak]
    if len(ppg_point) > 0:
        ptt.append(ppg_point[0] - j_peak)
    return np.array(ptt)


def estimate_bp(ptt, calibration_factor=1):
    return 120 - calibration_factor * ptt  # Example linear relation