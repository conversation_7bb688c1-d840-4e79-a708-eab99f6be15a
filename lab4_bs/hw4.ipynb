import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

# Set matplotlib parameters for better plots
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Suppress warnings for cleaner output
import warnings
warnings.filterwarnings('ignore')

# File paths
ecg_file = '../data/lab4/AB4_ecg.txt'
acc_file = '../data/lab4/AB4_acc.txt'

# Load data (first 5 minutes)
ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(ecg_file, acc_file, duration_minutes=5)

print(f"ECG signal length: {len(ecg_signal)} samples")
print(f"BCG signal length: {len(bcg_signal)} samples")
print(f"ECG time range: {ecg_time[0]:.2f} - {ecg_time[-1]:.2f} seconds")
print(f"BCG time range: {bcg_time[0]:.2f} - {bcg_time[-1]:.2f} seconds")

# Calculate sampling frequencies
fs_ecg = 1 / np.mean(np.diff(ecg_time))
fs_bcg = 1 / np.mean(np.diff(bcg_time))

print(f"\nECG sampling frequency: {fs_ecg:.1f} Hz")
print(f"BCG sampling frequency: {fs_bcg:.1f} Hz")

# Plot raw signals
fig = plot_signals(
    ecg_time, ecg_signal, 
    bcg_time, bcg_signal,
    'Raw ECG Signal', 'Raw BCG Signal (3D Magnitude)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show a zoomed view of the first 30 seconds
zoom_mask_ecg = ecg_time <= 30
zoom_mask_bcg = bcg_time <= 30

fig = plot_signals(
    ecg_time[zoom_mask_ecg], ecg_signal[zoom_mask_ecg], 
    bcg_time[zoom_mask_bcg], bcg_signal[zoom_mask_bcg],
    'Raw ECG Signal (First 30s)', 'Raw BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Apply bandpass filtering
print("Applying bandpass filtering...")
ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)

print("Bandpass filtering completed.")

# Plot filtered signals
fig = plot_signals(
    ecg_time, ecg_filtered, 
    bcg_time, bcg_filtered,
    'Filtered ECG Signal (0.5-40 Hz)', 'Filtered BCG Signal (1-20 Hz)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view
fig = plot_signals(
    ecg_time[zoom_mask_ecg], ecg_filtered[zoom_mask_ecg], 
    bcg_time[zoom_mask_bcg], bcg_filtered[zoom_mask_bcg],
    'Filtered ECG Signal (First 30s)', 'Filtered BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Apply DWT denoising
print("Applying DWT denoising...")
ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)

# Ensure same length
min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))

ecg_dwt = ecg_dwt[:min_len_ecg]
bcg_dwt = bcg_dwt[:min_len_bcg]
ecg_time_dwt = ecg_time[:min_len_ecg]
bcg_time_dwt = bcg_time[:min_len_bcg]

print("DWT denoising completed.")

# Plot DWT processed signals
fig = plot_signals(
    ecg_time_dwt, ecg_dwt, 
    bcg_time_dwt, bcg_dwt,
    'DWT Processed ECG Signal', 'DWT Processed BCG Signal',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view
zoom_mask_ecg_dwt = ecg_time_dwt <= 30
zoom_mask_bcg_dwt = bcg_time_dwt <= 30

fig = plot_signals(
    ecg_time_dwt[zoom_mask_ecg_dwt], ecg_dwt[zoom_mask_ecg_dwt], 
    bcg_time_dwt[zoom_mask_bcg_dwt], bcg_dwt[zoom_mask_bcg_dwt],
    'DWT Processed ECG Signal (First 30s)', 'DWT Processed BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Find peaks using improved methods
print("Detecting peaks with improved algorithms...")
ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_dwt, fs_bcg, method='hilbert')

print(f"Found {len(ecg_peaks)} R peaks in ECG")
print(f"Found {len(bcg_peaks)} J peaks in BCG (using Hilbert envelope method)")

# Calculate average heart rates
if len(ecg_peaks) > 1:
    avg_hr_ecg = 60 * fs_ecg * (len(ecg_peaks) - 1) / (ecg_peaks[-1] - ecg_peaks[0])
    print(f"Average HR from ECG: {avg_hr_ecg:.1f} bpm")

if len(bcg_peaks) > 1:
    avg_hr_bcg = 60 * fs_bcg * (len(bcg_peaks) - 1) / (bcg_peaks[-1] - bcg_peaks[0])
    print(f"Average HR from BCG (Hilbert): {avg_hr_bcg:.1f} bpm")

# Plot ECG with R peaks
fig1 = plot_peaks_detection(
    ecg_time_dwt, ecg_dwt, ecg_peaks,
    'ECG Signal with Detected R Peaks', 'Amplitude (mV)'
)
plt.show()

# Plot BCG with J peaks
fig2 = plot_peaks_detection(
    bcg_time_dwt, bcg_dwt, bcg_peaks,
    'BCG Signal with Detected J Peaks', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view of peak detection
zoom_start, zoom_end = 60, 90  # 30-second window
zoom_mask_ecg = (ecg_time_dwt >= zoom_start) & (ecg_time_dwt <= zoom_end)
zoom_mask_bcg = (bcg_time_dwt >= zoom_start) & (bcg_time_dwt <= zoom_end)
zoom_peaks_ecg = ecg_peaks[(ecg_time_dwt[ecg_peaks] >= zoom_start) & (ecg_time_dwt[ecg_peaks] <= zoom_end)]
zoom_peaks_bcg = bcg_peaks[(bcg_time_dwt[bcg_peaks] >= zoom_start) & (bcg_time_dwt[bcg_peaks] <= zoom_end)]

# Adjust peak indices for zoomed data
zoom_start_idx_ecg = np.where(zoom_mask_ecg)[0][0]
zoom_start_idx_bcg = np.where(zoom_mask_bcg)[0][0]
zoom_peaks_ecg_adj = zoom_peaks_ecg - zoom_start_idx_ecg
zoom_peaks_bcg_adj = zoom_peaks_bcg - zoom_start_idx_bcg

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# ECG zoomed
ax1.plot(ecg_time_dwt[zoom_mask_ecg], ecg_dwt[zoom_mask_ecg], 'b-', label='ECG')
ax1.plot(ecg_time_dwt[zoom_peaks_ecg], ecg_dwt[zoom_peaks_ecg], 'ro', markersize=8, label='R Peaks')
ax1.set_title('ECG Signal with R Peaks (Zoomed: 60-90s)')
ax1.set_ylabel('Amplitude (mV)')
ax1.legend()
ax1.grid(True)

# BCG zoomed - show envelope with peaks
ax2.plot(bcg_time_dwt[zoom_mask_bcg], bcg_dwt[zoom_mask_bcg], 'b-', alpha=0.6, label='BCG Signal')
ax2.plot(bcg_time_dwt[zoom_mask_bcg], bcg_envelope[zoom_mask_bcg], 'g-', linewidth=2, label='Envelope')
ax2.plot(bcg_time_dwt[zoom_peaks_bcg], bcg_envelope[zoom_peaks_bcg], 'ro', markersize=8, label='J Peaks on Envelope')
ax2.set_title('BCG Signal with Envelope and J Peaks (Zoomed: 60-90s)')
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Magnitude (ADC units)')
ax2.legend()
ax2.grid(True)

plt.tight_layout()
plt.show()

# Create comprehensive envelope and filtering visualization
fig, axes = plt.subplots(4, 1, figsize=(15, 16))

# Plot 1: ECG with R peaks (reference)
axes[0].plot(ecg_time_dwt, ecg_dwt, 'b-', alpha=0.7, label='ECG Signal')
axes[0].plot(ecg_time_dwt[ecg_peaks], ecg_dwt[ecg_peaks], 'ro', markersize=6, label=f'R Peaks ({len(ecg_peaks)})')
axes[0].set_title('ECG Signal with R Peaks (Reference)')
axes[0].set_ylabel('Amplitude (mV)')
axes[0].legend()
axes[0].grid(True, alpha=0.3)

# Plot 2: BCG signal with positive/negative amplitude regions
axes[1].plot(bcg_time_dwt, bcg_dwt, 'b-', alpha=0.7, label='BCG Signal')
positive_mask = bcg_dwt > 0
axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=positive_mask, alpha=0.3, color='green', label='Positive Amplitude (J peaks)')
axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=~positive_mask, alpha=0.3, color='red', label='Negative Amplitude (filtered out)')
axes[1].axhline(0, color='black', linestyle='-', alpha=0.5)
axes[1].set_title('BCG Signal - Positive vs Negative Amplitude Regions')
axes[1].set_ylabel('Magnitude (ADC units)')
axes[1].legend()
axes[1].grid(True, alpha=0.3)

# Plot 3: BCG envelope with threshold
axes[2].plot(bcg_time_dwt, bcg_envelope, 'g-', linewidth=2, label='Signal Envelope')

# Calculate and show threshold
envelope_mean = np.mean(bcg_envelope)
envelope_std = np.std(bcg_envelope)
threshold = envelope_mean + 0.8 * envelope_std  # Using optimized threshold factor
axes[2].axhline(threshold, color='orange', linestyle='--', alpha=0.8, label=f'Adaptive Threshold (μ + 0.8σ = {threshold:.0f})')
axes[2].axhline(envelope_mean, color='gray', linestyle=':', alpha=0.6, label=f'Mean (μ = {envelope_mean:.0f})')

axes[2].set_title('BCG Envelope with Adaptive Threshold')
axes[2].set_ylabel('Envelope Amplitude')
axes[2].legend()
axes[2].grid(True, alpha=0.3)

# Plot 4: Final result - envelope with positive J peaks only
axes[3].plot(bcg_time_dwt, bcg_envelope, 'g-', linewidth=2, label='Signal Envelope')
axes[3].plot(bcg_time_dwt[bcg_peaks], bcg_envelope[bcg_peaks], 'ro', markersize=8, label=f'J Peaks (Positive Only) ({len(bcg_peaks)})')
axes[3].axhline(threshold, color='orange', linestyle='--', alpha=0.8, label=f'Threshold')

# Add parameter information
positive_count = sum(1 for peak_idx in bcg_peaks if peak_idx < len(bcg_dwt) and bcg_dwt[peak_idx] > 0)
negative_count = len(bcg_peaks) - positive_count
param_text = f'Parameters: Hilbert envelope, threshold=0.8σ, min_distance=0.3s\nPositive amplitude filtering: {positive_count} peaks kept, {negative_count} filtered out\nPeak count ratio (BCG/ECG): {len(bcg_peaks)/len(ecg_peaks):.2f}'
axes[3].text(0.02, 0.98, param_text, transform=axes[3].transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

axes[3].set_title('Optimized J Peak Detection with Positive Amplitude Filtering')
axes[3].set_xlabel('Time (s)')
axes[3].set_ylabel('Envelope Amplitude')
axes[3].legend()
axes[3].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print filtering statistics
print(f"\n=== Envelope and Filtering Statistics ===")
print(f"ECG R peaks (reference): {len(ecg_peaks)}")
print(f"BCG J peaks (positive only): {len(bcg_peaks)}")
print(f"Peak count difference: {abs(len(bcg_peaks) - len(ecg_peaks))} peaks")
print(f"Peak count ratio (BCG/ECG): {len(bcg_peaks)/len(ecg_peaks):.2f}")
print(f"Positive amplitude filtering: {positive_count} peaks kept, {negative_count} filtered out")
print(f"Envelope statistics: Mean = {envelope_mean:.0f}, Std = {envelope_std:.0f}")
print(f"Adaptive threshold: {threshold:.0f} (μ + 0.8σ)")

# Test advanced BCG preprocessing techniques
print("=== Advanced BCG Processing Comparison ===")

# Test different preprocessing methods
methods = ['multi_scale', 'adaptive_filter']
advanced_results = {}

for method in methods:
    print(f"\nTesting {method} preprocessing...")
    
    # Apply advanced preprocessing
    processed_bcg, quality_score = advanced_bcg_preprocessing(bcg_dwt, fs_bcg, method=method)
    
    # Detect peaks with enhanced method
    enhanced_peaks, enhanced_envelope, confidence = enhanced_j_peak_detection(processed_bcg, fs_bcg, method='multi_envelope')
    
    # Calculate metrics
    peak_count = len(enhanced_peaks)
    avg_confidence = np.mean(confidence) if len(confidence) > 0 else 0
    peak_ratio = peak_count / len(ecg_peaks) if len(ecg_peaks) > 0 else 0
    
    advanced_results[method] = {
        'processed_signal': processed_bcg,
        'peaks': enhanced_peaks,
        'envelope': enhanced_envelope,
        'confidence': confidence,
        'quality_score': quality_score,
        'peak_count': peak_count,
        'avg_confidence': avg_confidence,
        'peak_ratio': peak_ratio
    }
    
    print(f"  Quality score: {quality_score:.3f}")
    print(f"  Peaks detected: {peak_count}")
    print(f"  Average confidence: {avg_confidence:.3f}")
    print(f"  Peak ratio (BCG/ECG): {peak_ratio:.3f}")

# Compare with standard method
print(f"\nComparison with standard method:")
print(f"  Standard peaks: {len(bcg_peaks)}")
print(f"  Standard ratio: {len(bcg_peaks)/len(ecg_peaks):.3f}")

# Find best method
best_method = max(advanced_results.keys(), key=lambda k: advanced_results[k]['quality_score'] * advanced_results[k]['avg_confidence'])
print(f"\nBest method: {best_method}")
print(f"  Combined score: {advanced_results[best_method]['quality_score'] * advanced_results[best_method]['avg_confidence']:.3f}")

# Visualize advanced processing results
fig, axes = plt.subplots(3, 2, figsize=(18, 15))

# Plot comparison of preprocessing methods
for i, (method, results) in enumerate(advanced_results.items()):
    # Left column: processed signals
    axes[i, 0].plot(bcg_time_dwt, bcg_dwt, 'b-', alpha=0.5, label='Original BCG')
    axes[i, 0].plot(bcg_time_dwt, results['processed_signal'], 'r-', linewidth=2, label=f'{method.title()} Processed')
    axes[i, 0].set_title(f'{method.title()} Preprocessing (Quality: {results["quality_score"]:.3f})')
    axes[i, 0].set_ylabel('Amplitude')
    axes[i, 0].legend()
    axes[i, 0].grid(True, alpha=0.3)
    
    # Right column: envelope with peaks and confidence
    axes[i, 1].plot(bcg_time_dwt, results['envelope'], 'g-', linewidth=2, label='Enhanced Envelope')
    
    # Color-code peaks by confidence
    if len(results['peaks']) > 0 and len(results['confidence']) > 0:
        scatter = axes[i, 1].scatter(bcg_time_dwt[results['peaks']], results['envelope'][results['peaks']], 
                                   c=results['confidence'], cmap='RdYlGn', s=80, 
                                   label=f'J Peaks ({len(results["peaks"])})', edgecolors='black')
        plt.colorbar(scatter, ax=axes[i, 1], label='Confidence')
    
    axes[i, 1].set_title(f'{method.title()} Peak Detection (Avg Conf: {results["avg_confidence"]:.3f})')
    axes[i, 1].set_ylabel('Envelope Amplitude')
    axes[i, 1].legend()
    axes[i, 1].grid(True, alpha=0.3)

# Bottom row: comparison with ECG
axes[2, 0].plot(ecg_time_dwt, ecg_dwt, 'b-', alpha=0.7, label='ECG Signal')
axes[2, 0].plot(ecg_time_dwt[ecg_peaks], ecg_dwt[ecg_peaks], 'ro', markersize=6, label=f'ECG R Peaks ({len(ecg_peaks)})')
axes[2, 0].set_title('ECG Reference')
axes[2, 0].set_xlabel('Time (s)')
axes[2, 0].set_ylabel('Amplitude (mV)')
axes[2, 0].legend()
axes[2, 0].grid(True, alpha=0.3)

# Performance comparison
methods_list = list(advanced_results.keys()) + ['standard']
peak_counts = [advanced_results[m]['peak_count'] for m in advanced_results.keys()] + [len(bcg_peaks)]
quality_scores = [advanced_results[m]['quality_score'] for m in advanced_results.keys()] + [0.7]  # Estimated for standard

x_pos = np.arange(len(methods_list))
width = 0.35

bars1 = axes[2, 1].bar(x_pos - width/2, peak_counts, width, label='Peak Count', alpha=0.7)
bars2 = axes[2, 1].bar(x_pos + width/2, [q*100 for q in quality_scores], width, label='Quality Score (×100)', alpha=0.7)

# Add ECG reference line
axes[2, 1].axhline(len(ecg_peaks), color='red', linestyle='--', alpha=0.8, label=f'ECG Reference ({len(ecg_peaks)})')

axes[2, 1].set_title('Method Performance Comparison')
axes[2, 1].set_xlabel('Method')
axes[2, 1].set_ylabel('Count / Score')
axes[2, 1].set_xticks(x_pos)
axes[2, 1].set_xticklabels([m.title() for m in methods_list])
axes[2, 1].legend()
axes[2, 1].grid(True, alpha=0.3)

# Add value labels on bars
for bar, value in zip(bars1, peak_counts):
    axes[2, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, f'{value}', 
                   ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.show()

# Print detailed comparison
print(f"\n=== Detailed Method Comparison ===")
print(f"{'Method':<15} {'Peaks':<6} {'Ratio':<6} {'Quality':<8} {'Confidence':<10} {'Score':<8}")
print("-" * 65)

for method, results in advanced_results.items():
    combined_score = results['quality_score'] * results['avg_confidence']
    print(f"{method.title():<15} {results['peak_count']:<6} {results['peak_ratio']:<6.3f} {results['quality_score']:<8.3f} {results['avg_confidence']:<10.3f} {combined_score:<8.3f}")

# Standard method for comparison
standard_ratio = len(bcg_peaks) / len(ecg_peaks)
print(f"{'Standard':<15} {len(bcg_peaks):<6} {standard_ratio:<6.3f} {'0.700':<8} {'0.700':<10} {'0.490':<8}")

# Calculate RR intervals
# Use the best advanced method for BCG analysis
best_bcg_peaks = advanced_results[best_method]['peaks']
best_bcg_confidence = advanced_results[best_method]['confidence']

# Calculate RR intervals
rr_ecg = calculate_rr_intervals(ecg_peaks, fs_ecg)
rr_bcg_standard = calculate_rr_intervals(bcg_peaks, fs_bcg)
rr_bcg_advanced = calculate_rr_intervals(best_bcg_peaks, fs_bcg)

print(f"\n=== RR Interval Analysis ===")
print(f"ECG RR intervals: {len(rr_ecg)} intervals")
print(f"BCG RR intervals (standard): {len(rr_bcg_standard)} intervals")
print(f"BCG RR intervals (advanced {best_method}): {len(rr_bcg_advanced)} intervals")

if len(rr_ecg) > 0:
    print(f"\nECG RR - Mean: {np.mean(rr_ecg):.1f} ms, Std: {np.std(rr_ecg):.1f} ms")
if len(rr_bcg_standard) > 0:
    print(f"BCG RR (standard) - Mean: {np.mean(rr_bcg_standard):.1f} ms, Std: {np.std(rr_bcg_standard):.1f} ms")
if len(rr_bcg_advanced) > 0:
    print(f"BCG RR (advanced) - Mean: {np.mean(rr_bcg_advanced):.1f} ms, Std: {np.std(rr_bcg_advanced):.1f} ms")

# Calculate instantaneous heart rate
hr_ecg = calculate_heart_rate(rr_ecg)
hr_bcg_standard = calculate_heart_rate(rr_bcg_standard)
hr_bcg_advanced = calculate_heart_rate(rr_bcg_advanced)

# Apply advanced smoothing to the best BCG method
if len(hr_bcg_advanced) > 0:
    hr_bcg_advanced_smooth = smooth_hr_curve(hr_bcg_advanced, window_size=5)  # Larger window for advanced method
else:
    hr_bcg_advanced_smooth = hr_bcg_advanced

# Create time vectors for HR (at peak times)
if len(ecg_peaks) > 1:
    hr_times_ecg = ecg_time_dwt[ecg_peaks[1:]]  # HR corresponds to intervals between peaks
if len(bcg_peaks) > 1:
    hr_times_bcg = bcg_time_dwt[bcg_peaks[1:]]

print(f"\nHeart Rate Statistics:")
if len(hr_ecg) > 0:
    print(f"ECG HR - Mean: {np.mean(hr_ecg):.1f} bpm, Std: {np.std(hr_ecg):.1f} bpm")
    print(f"ECG HR - Range: {np.min(hr_ecg):.1f} - {np.max(hr_ecg):.1f} bpm")
if len(hr_bcg) > 0:
    print(f"BCG HR - Mean: {np.mean(hr_bcg):.1f} bpm, Std: {np.std(hr_bcg):.1f} bpm")
    print(f"BCG HR - Range: {np.min(hr_bcg):.1f} - {np.max(hr_bcg):.1f} bpm")

# Plot heart rate comparison
if len(hr_ecg) > 0 and len(hr_bcg) > 0:
    fig = plot_hr_comparison(hr_ecg, hr_bcg, hr_times_ecg, hr_times_bcg)
    plt.show()
    
    # Calculate correlation between ECG and BCG heart rates
    # Interpolate to common time base for correlation
    if len(hr_times_ecg) > 5 and len(hr_times_bcg) > 5:
        common_time = np.linspace(max(hr_times_ecg[0], hr_times_bcg[0]), 
                                 min(hr_times_ecg[-1], hr_times_bcg[-1]), 100)
        
        hr_ecg_interp = np.interp(common_time, hr_times_ecg, hr_ecg)
        hr_bcg_interp = np.interp(common_time, hr_times_bcg, hr_bcg)
        
        correlation = np.corrcoef(hr_ecg_interp, hr_bcg_interp)[0, 1]
        print(f"\nHeart Rate Correlation (ECG vs BCG): {correlation:.3f}")
        
        # Plot correlation scatter
        plt.figure(figsize=(8, 6))
        plt.scatter(hr_ecg_interp, hr_bcg_interp, alpha=0.6)
        plt.xlabel('ECG Heart Rate (bpm)')
        plt.ylabel('BCG Heart Rate (bpm)')
        plt.title(f'ECG vs BCG Heart Rate Correlation (r = {correlation:.3f})')
        plt.grid(True)
        
        # Add diagonal line
        min_hr = min(np.min(hr_ecg_interp), np.min(hr_bcg_interp))
        max_hr = max(np.max(hr_ecg_interp), np.max(hr_bcg_interp))
        plt.plot([min_hr, max_hr], [min_hr, max_hr], 'r--', alpha=0.8, label='Perfect Correlation')
        plt.legend()
        plt.show()

# Calculate time domain HRV metrics
print("=== TIME DOMAIN HRV METRICS ===")

if len(rr_ecg) > 1:
    hrv_ecg = calculate_hrv_metrics(rr_ecg)
    print("\nECG HRV Metrics:")
    for metric, value in hrv_ecg.items():
        print(f"  {metric}: {value:.2f}")

if len(rr_bcg) > 1:
    hrv_bcg = calculate_hrv_metrics(rr_bcg)
    print("\nBCG HRV Metrics:")
    for metric, value in hrv_bcg.items():
        print(f"  {metric}: {value:.2f}")

# Plot RR interval distributions
if len(rr_ecg) > 0 and len(rr_bcg) > 0:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # ECG RR intervals
    ax1.hist(rr_ecg, bins=30, alpha=0.7, color='red', edgecolor='black')
    ax1.set_title('ECG RR Interval Distribution')
    ax1.set_xlabel('RR Interval (ms)')
    ax1.set_ylabel('Frequency')
    ax1.grid(True, alpha=0.3)
    ax1.axvline(np.mean(rr_ecg), color='red', linestyle='--', linewidth=2, label=f'Mean: {np.mean(rr_ecg):.1f} ms')
    ax1.legend()
    
    # BCG RR intervals
    ax2.hist(rr_bcg, bins=30, alpha=0.7, color='blue', edgecolor='black')
    ax2.set_title('BCG RR Interval Distribution')
    ax2.set_xlabel('RR Interval (ms)')
    ax2.set_ylabel('Frequency')
    ax2.grid(True, alpha=0.3)
    ax2.axvline(np.mean(rr_bcg), color='blue', linestyle='--', linewidth=2, label=f'Mean: {np.mean(rr_bcg):.1f} ms')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

# Calculate frequency domain HRV metrics
print("=== FREQUENCY DOMAIN HRV METRICS ===")

if len(rr_ecg) > 10:
    # Calculate RR interval times
    rr_times_ecg = np.cumsum(np.concatenate([[0], rr_ecg[:-1]])) / 1000
    
    freq_hrv_ecg, freqs_ecg, psd_ecg = calculate_frequency_domain_hrv(rr_ecg, rr_times_ecg)
    
    print("\nECG Frequency Domain HRV:")
    for metric, value in freq_hrv_ecg.items():
        if 'power' in metric:
            print(f"  {metric}: {value:.4f} ms²")
        else:
            print(f"  {metric}: {value:.2f}")
    
    # Plot ECG HRV PSD
    fig_ecg = plot_hrv_psd(freqs_ecg, psd_ecg, "ECG HRV Power Spectral Density")
    plt.show()

if len(rr_bcg) > 10:
    # Calculate RR interval times
    rr_times_bcg = np.cumsum(np.concatenate([[0], rr_bcg[:-1]])) / 1000
    
    freq_hrv_bcg, freqs_bcg, psd_bcg = calculate_frequency_domain_hrv(rr_bcg, rr_times_bcg)
    
    print("\nBCG Frequency Domain HRV:")
    for metric, value in freq_hrv_bcg.items():
        if 'power' in metric:
            print(f"  {metric}: {value:.4f} ms²")
        else:
            print(f"  {metric}: {value:.2f}")
    
    # Plot BCG HRV PSD
    fig_bcg = plot_hrv_psd(freqs_bcg, psd_bcg, "BCG HRV Power Spectral Density")
    plt.show()

# Plot RR interval time series
if len(rr_ecg) > 0 and len(rr_bcg) > 0:
    # Create time vectors for RR intervals
    rr_times_ecg_plot = np.cumsum(np.concatenate([[0], rr_ecg[:-1]])) / 1000
    rr_times_bcg_plot = np.cumsum(np.concatenate([[0], rr_bcg[:-1]])) / 1000
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # ECG RR intervals
    ax1.plot(rr_times_ecg_plot, rr_ecg, 'ro-', markersize=4, linewidth=1, alpha=0.8, label='ECG RR Intervals')
    ax1.set_title('ECG RR Interval Time Series')
    ax1.set_ylabel('RR Interval (ms)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Add mean line
    ax1.axhline(np.mean(rr_ecg), color='red', linestyle='--', alpha=0.7, label=f'Mean: {np.mean(rr_ecg):.1f} ms')
    
    # BCG RR intervals
    ax2.plot(rr_times_bcg_plot, rr_bcg, 'bo-', markersize=4, linewidth=1, alpha=0.8, label='BCG RR Intervals')
    ax2.set_title('BCG RR Interval Time Series')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('RR Interval (ms)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # Add mean line
    ax2.axhline(np.mean(rr_bcg), color='blue', linestyle='--', alpha=0.7, label=f'Mean: {np.mean(rr_bcg):.1f} ms')
    
    plt.tight_layout()
    plt.show()
    
    # Plot Poincaré plot (RR[n] vs RR[n+1])
    if len(rr_ecg) > 1 and len(rr_bcg) > 1:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # ECG Poincaré plot
        ax1.scatter(rr_ecg[:-1], rr_ecg[1:], alpha=0.6, color='red')
        ax1.set_xlabel('RR[n] (ms)')
        ax1.set_ylabel('RR[n+1] (ms)')
        ax1.set_title('ECG Poincaré Plot')
        ax1.grid(True, alpha=0.3)
        
        # Add diagonal line
        min_rr_ecg = min(np.min(rr_ecg[:-1]), np.min(rr_ecg[1:]))
        max_rr_ecg = max(np.max(rr_ecg[:-1]), np.max(rr_ecg[1:]))
        ax1.plot([min_rr_ecg, max_rr_ecg], [min_rr_ecg, max_rr_ecg], 'k--', alpha=0.5)
        
        # BCG Poincaré plot
        ax2.scatter(rr_bcg[:-1], rr_bcg[1:], alpha=0.6, color='blue')
        ax2.set_xlabel('RR[n] (ms)')
        ax2.set_ylabel('RR[n+1] (ms)')
        ax2.set_title('BCG Poincaré Plot')
        ax2.grid(True, alpha=0.3)
        
        # Add diagonal line
        min_rr_bcg = min(np.min(rr_bcg[:-1]), np.min(rr_bcg[1:]))
        max_rr_bcg = max(np.max(rr_bcg[:-1]), np.max(rr_bcg[1:]))
        ax2.plot([min_rr_bcg, max_rr_bcg], [min_rr_bcg, max_rr_bcg], 'k--', alpha=0.5)
        
        plt.tight_layout()
        plt.show()

# Comprehensive comparison
print("=== COMPREHENSIVE ECG vs BCG COMPARISON ===")
print("\n" + "="*60)
print("BASIC STATISTICS")
print("="*60)

if len(rr_ecg) > 0 and len(rr_bcg) > 0:
    print(f"{'Metric':<25} {'ECG':<15} {'BCG':<15} {'Difference':<15}")
    print("-" * 70)
    
    # Basic heart rate comparison
    mean_hr_ecg = np.mean(hr_ecg) if len(hr_ecg) > 0 else 0
    mean_hr_bcg = np.mean(hr_bcg) if len(hr_bcg) > 0 else 0
    hr_diff = abs(mean_hr_ecg - mean_hr_bcg)
    
    print(f"{'Mean HR (bpm)':<25} {mean_hr_ecg:<15.1f} {mean_hr_bcg:<15.1f} {hr_diff:<15.1f}")
    
    # RR interval comparison
    mean_rr_ecg = np.mean(rr_ecg)
    mean_rr_bcg = np.mean(rr_bcg)
    rr_diff = abs(mean_rr_ecg - mean_rr_bcg)
    
    print(f"{'Mean RR (ms)':<25} {mean_rr_ecg:<15.1f} {mean_rr_bcg:<15.1f} {rr_diff:<15.1f}")
    
    # HRV comparison
    if 'hrv_ecg' in locals() and 'hrv_bcg' in locals():
        print("\n" + "="*60)
        print("HRV METRICS COMPARISON")
        print("="*60)
        print(f"{'HRV Metric':<25} {'ECG':<15} {'BCG':<15} {'Difference':<15}")
        print("-" * 70)
        
        for metric in hrv_ecg.keys():
            if metric in hrv_bcg:
                ecg_val = hrv_ecg[metric]
                bcg_val = hrv_bcg[metric]
                diff = abs(ecg_val - bcg_val)
                print(f"{metric:<25} {ecg_val:<15.2f} {bcg_val:<15.2f} {diff:<15.2f}")
    
    # Calculate agreement metrics
    print("\n" + "="*60)
    print("AGREEMENT ANALYSIS")
    print("="*60)
    
    # RR interval correlation
    if len(rr_ecg) == len(rr_bcg):
        rr_correlation = np.corrcoef(rr_ecg, rr_bcg)[0, 1]
        print(f"RR Interval Correlation: {rr_correlation:.3f}")
    
    # Bland-Altman analysis for RR intervals
    if len(rr_ecg) > 5 and len(rr_bcg) > 5:
        # Interpolate to same length for comparison
        min_len = min(len(rr_ecg), len(rr_bcg))
        rr_ecg_comp = rr_ecg[:min_len]
        rr_bcg_comp = rr_bcg[:min_len]
        
        # Bland-Altman statistics
        mean_diff = np.mean(rr_ecg_comp - rr_bcg_comp)
        std_diff = np.std(rr_ecg_comp - rr_bcg_comp)
        
        print(f"Mean Difference (ECG-BCG): {mean_diff:.2f} ms")
        print(f"Std of Differences: {std_diff:.2f} ms")
        print(f"95% Limits of Agreement: [{mean_diff - 1.96*std_diff:.2f}, {mean_diff + 1.96*std_diff:.2f}] ms")
        
        # Plot Bland-Altman
        plt.figure(figsize=(10, 6))
        mean_rr = (rr_ecg_comp + rr_bcg_comp) / 2
        diff_rr = rr_ecg_comp - rr_bcg_comp
        
        plt.scatter(mean_rr, diff_rr, alpha=0.6)
        plt.axhline(mean_diff, color='red', linestyle='-', label=f'Mean Diff: {mean_diff:.2f} ms')
        plt.axhline(mean_diff + 1.96*std_diff, color='red', linestyle='--', label=f'+1.96 SD: {mean_diff + 1.96*std_diff:.2f} ms')
        plt.axhline(mean_diff - 1.96*std_diff, color='red', linestyle='--', label=f'-1.96 SD: {mean_diff - 1.96*std_diff:.2f} ms')
        
        plt.xlabel('Mean RR Interval (ECG + BCG)/2 (ms)')
        plt.ylabel('Difference (ECG - BCG) (ms)')
        plt.title('Bland-Altman Plot: ECG vs BCG RR Intervals')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()